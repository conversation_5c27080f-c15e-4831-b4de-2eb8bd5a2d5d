{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Prescription
            </a>
        </div>
    </div>

    <!-- Billing Office Alert -->
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <strong>Billing Office Interface:</strong> This is the billing office interface for processing medication payments. 
        You can accept payments directly or process payments from the patient's wallet.
    </div>

    <div class="row">
        <!-- Patient & Prescription Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i> Patient Information
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>Name:</strong></td>
                            <td>{{ prescription.patient.get_full_name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Patient ID:</strong></td>
                            <td>{{ prescription.patient.patient_id }}</td>
                        </tr>
                        <tr>
                            <td><strong>Phone:</strong></td>
                            <td>{{ prescription.patient.phone_number|default:"N/A" }}</td>
                        </tr>
                        <tr>
                            <td><strong>NHIA Status:</strong></td>
                            <td>
                                {% if pricing_breakdown.is_nhia_patient %}
                                    <span class="badge bg-success">NHIA Patient</span>
                                {% else %}
                                    <span class="badge bg-secondary">Non-NHIA</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Wallet Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-wallet"></i> Patient Wallet
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h4 class="text-success">₦{{ patient_wallet.balance|floatformat:2 }}</h4>
                        <small class="text-muted">Available Balance</small>
                        {% if patient_wallet.balance >= remaining_amount %}
                            <div class="mt-2">
                                <span class="badge bg-success">Sufficient Funds</span>
                            </div>
                        {% else %}
                            <div class="mt-2">
                                <span class="badge bg-warning">Insufficient Funds</span>
                                <small class="d-block text-muted mt-1">
                                    Short by ₦{{ remaining_amount|floatformat:2|add:patient_wallet.balance|floatformat:2 }}
                                </small>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Form -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-credit-card"></i> Process Payment
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Pricing Breakdown -->
                    <div class="alert alert-light border">
                        <h6 class="alert-heading">
                            <i class="fas fa-calculator"></i> Pricing Breakdown
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td>Total Medication Cost:</td>
                                        <td class="text-end"><strong>₦{{ pricing_breakdown.total_cost|floatformat:2 }}</strong></td>
                                    </tr>
                                    {% if pricing_breakdown.is_nhia_patient %}
                                    <tr>
                                        <td>NHIA Coverage (90%):</td>
                                        <td class="text-end text-success">-₦{{ pricing_breakdown.nhia_coverage|floatformat:2 }}</td>
                                    </tr>
                                    <tr class="border-top">
                                        <td><strong>Patient Pays (10%):</strong></td>
                                        <td class="text-end"><strong>₦{{ pricing_breakdown.patient_pays|floatformat:2 }}</strong></td>
                                    </tr>
                                    {% else %}
                                    <tr class="border-top">
                                        <td><strong>Patient Pays (100%):</strong></td>
                                        <td class="text-end"><strong>₦{{ pricing_breakdown.patient_pays|floatformat:2 }}</strong></td>
                                    </tr>
                                    {% endif %}
                                </table>
                            </div>
                            <div class="col-md-6">
                                <div class="text-center">
                                    <h3 class="text-primary">₦{{ remaining_amount|floatformat:2 }}</h3>
                                    <small class="text-muted">Amount Due</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Form -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Payment Source Selection -->
                        <div class="mb-4">
                            <label class="form-label"><strong>Payment Source</strong></label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-primary">
                                        <div class="card-body text-center">
                                            <input type="radio" name="payment_source" value="billing_office" 
                                                   id="billing_office" class="form-check-input" checked>
                                            <label for="billing_office" class="form-check-label">
                                                <i class="fas fa-building fa-2x text-primary d-block mb-2"></i>
                                                <strong>Billing Office Payment</strong>
                                                <small class="d-block text-muted">Cash, Card, or Bank Transfer</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-body text-center">
                                            <input type="radio" name="payment_source" value="patient_wallet" 
                                                   id="patient_wallet" class="form-check-input"
                                                   {% if patient_wallet.balance < remaining_amount %}disabled{% endif %}>
                                            <label for="patient_wallet" class="form-check-label">
                                                <i class="fas fa-wallet fa-2x text-success d-block mb-2"></i>
                                                <strong>Patient Wallet</strong>
                                                <small class="d-block text-muted">
                                                    Balance: ₦{{ patient_wallet.balance|floatformat:2 }}
                                                </small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Details -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.amount.id_for_label }}" class="form-label">Amount</label>
                                    {{ form.amount }}
                                    {% if form.amount.errors %}
                                        <div class="text-danger">{{ form.amount.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.payment_date.id_for_label }}" class="form-label">Payment Date</label>
                                    {{ form.payment_date }}
                                    {% if form.payment_date.errors %}
                                        <div class="text-danger">{{ form.payment_date.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row" id="billing-office-fields">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.payment_method.id_for_label }}" class="form-label">Payment Method</label>
                                    {{ form.payment_method }}
                                    {% if form.payment_method.errors %}
                                        <div class="text-danger">{{ form.payment_method.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.transaction_id.id_for_label }}" class="form-label">Transaction ID (Optional)</label>
                                    {{ form.transaction_id }}
                                    {% if form.transaction_id.errors %}
                                        <div class="text-danger">{{ form.transaction_id.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">Notes (Optional)</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger">{{ form.notes.errors }}</div>
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-check"></i> Process Payment (₦{{ remaining_amount|floatformat:2 }})
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const billingOfficeRadio = document.getElementById('billing_office');
    const walletRadio = document.getElementById('patient_wallet');
    const billingOfficeFields = document.getElementById('billing-office-fields');
    const paymentMethodField = document.querySelector('[name="payment_method"]');
    
    function togglePaymentFields() {
        if (walletRadio.checked) {
            billingOfficeFields.style.display = 'none';
            paymentMethodField.value = 'wallet';
        } else {
            billingOfficeFields.style.display = 'block';
            if (paymentMethodField.value === 'wallet') {
                paymentMethodField.value = 'cash';
            }
        }
    }
    
    billingOfficeRadio.addEventListener('change', togglePaymentFields);
    walletRadio.addEventListener('change', togglePaymentFields);
    
    // Initial setup
    togglePaymentFields();
});
</script>
{% endblock %}
