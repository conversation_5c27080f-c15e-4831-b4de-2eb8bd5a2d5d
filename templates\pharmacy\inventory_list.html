{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex flex-column flex-md-row align-items-start align-items-md-center justify-content-between mb-4">
                <div class="mb-3 mb-md-0">
                    <h1 class="h3 mb-1 text-gray-800">
                        <i class="fas fa-pills text-primary"></i> {{ title }}
                    </h1>
                    <p class="text-muted mb-0">Manage your medication inventory and procurement</p>
                </div>
                <div class="d-flex flex-wrap gap-2">
                    <a href="{% url 'pharmacy:add_medication' %}" class="btn btn-success btn-sm">
                        <i class="fas fa-plus"></i> Add Medication
                    </a>
                    <a href="{% url 'pharmacy:manage_categories' %}" class="btn btn-info btn-sm">
                        <i class="fas fa-tags"></i> Categories
                    </a>
                    <a href="{% url 'pharmacy:supplier_list' %}" class="btn btn-warning btn-sm">
                        <i class="fas fa-truck"></i> Suppliers
                    </a>
                    <a href="{% url 'pharmacy:procurement_dashboard' %}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-shopping-cart"></i> Procurement
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-search"></i> Search & Filter Medications
                    </h6>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-lg-4 col-md-6">
                            <label for="{{ form.search.id_for_label }}" class="form-label fw-semibold">
                                <i class="fas fa-search text-muted"></i> Search Medications
                            </label>
                            {{ form.search }}
                            {% if form.search.errors %}
                                <div class="text-danger small">{{ form.search.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label for="{{ form.category.id_for_label }}" class="form-label fw-semibold">
                                <i class="fas fa-tags text-muted"></i> Category
                            </label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="text-danger small">{{ form.category.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label for="{{ form.is_active.id_for_label }}" class="form-label fw-semibold">
                                <i class="fas fa-toggle-on text-muted"></i> Status
                            </label>
                            {{ form.is_active }}
                            {% if form.is_active.errors %}
                                <div class="text-danger small">{{ form.is_active.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-lg-2 col-md-6 d-flex align-items-end">
                            <div class="d-grid gap-2 w-100">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <a href="{% url 'pharmacy:inventory' %}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-redo"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Medications</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ page_obj.paginator.count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-pills fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Medications</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% for med in page_obj %}{% if med.is_active %}{{ forloop.counter }}{% endif %}{% endfor %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Categories</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ page_obj|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Quick Actions</div>
                            <div class="mt-2">
                                <a href="{% url 'pharmacy:add_medication' %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus"></i> Add Medication
                                </a>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bolt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
                                <h2 class="mb-0">{{ total_medications }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">In Stock</h5>
                                <h2 class="mb-0">{{ in_stock_count }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Low Stock</h5>
                                <h2 class="mb-0">{{ low_stock_count }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Out of Stock</h5>
                                <h2 class="mb-0">{{ out_of_stock_count }}</h2>
                            </div>
                        </div>
                    </div>
                </div>



    <!-- Medication Inventory List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                    <h6 class="m-0 font-weight-bold text-primary mb-2 mb-md-0">
                        <i class="fas fa-pills"></i> Medication Inventory
                        <span class="badge bg-primary ms-2">{{ page_obj.paginator.count }} total</span>
                    </h6>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="{% url 'pharmacy:supplier_list' %}" class="btn btn-sm btn-info">
                            <i class="fas fa-truck"></i> Suppliers
                        </a>
                        <a href="{% url 'pharmacy:add_medication' %}" class="btn btn-sm btn-success">
                            <i class="fas fa-plus"></i> Add Medication
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if page_obj %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" width="100%" cellspacing="0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0">Medication</th>
                                    <th class="border-0">Category</th>
                                    <th class="border-0">Form & Strength</th>
                                    <th class="border-0">Price</th>
                                    <th class="border-0">Stock Status</th>
                                    <th class="border-0">Status</th>
                                    <th class="border-0 text-center">Actions</th>
                                </tr>
                            </thead>
                    <tbody>
                        {% for medication in page_obj %}
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ medication.name }}</strong>
                                    {% if medication.generic_name %}
                                    <small class="text-muted d-block">{{ medication.generic_name }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if medication.category %}
                                <span class="badge bg-secondary">{{ medication.category.name }}</span>
                                {% else %}
                                <span class="text-muted">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    <span class="text-primary">{{ medication.dosage_form }}</span>
                                    {% if medication.strength %}
                                    <small class="d-block text-muted">{{ medication.strength }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <strong class="text-success">₦{{ medication.price }}</strong>
                            </td>
                            <td>
                                {% for inventory in medication.inventories.all %}
                                    <div class="mb-1">
                                        <small class="text-muted">{{ inventory.dispensary.name }}:</small>
                                        <span class="badge {% if inventory.is_low_stock %}bg-warning{% elif inventory.stock_quantity > 0 %}bg-success{% else %}bg-danger{% endif %}">
                                            {{ inventory.stock_quantity }} units
                                        </span>
                                        {% if inventory.is_low_stock %}
                                        <small class="text-warning d-block">Low Stock!</small>
                                        {% endif %}
                                    </div>
                                {% empty %}
                                    <span class="badge bg-secondary">No inventory</span>
                                {% endfor %}
                            </td>
                            <td>
                                {% if medication.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{% url 'pharmacy:medication_detail' medication.id %}"
                                       class="btn btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'pharmacy:edit_medication' medication.id %}"
                                       class="btn btn-outline-info" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-success"
                                            data-bs-toggle="modal"
                                            data-bs-target="#procureModal{{ medication.id }}"
                                            title="Procure from Supplier">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                    {% if medication.is_active %}
                                    <a href="{% url 'pharmacy:delete_medication' medication.id %}"
                                       class="btn btn-outline-danger" title="Deactivate">
                                        <i class="fas fa-ban"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No medications found</h5>
                <p class="text-muted">
                    {% if request.GET.search or request.GET.category or request.GET.is_active %}
                        No medications match your search criteria. Try adjusting your filters.
                    {% else %}
                        Get started by adding your first medication.
                    {% endif %}
                </p>
                <a href="{% url 'pharmacy:add_medication' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add First Medication
                </a>
            </div>
            {% endif %}
        </div>
    </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
<!-- Procurement Modals -->
{% for medication in page_obj %}
<div class="modal fade" id="procureModal{{ medication.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Procure {{ medication.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'pharmacy:create_procurement_request' medication.id %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="supplier{{ medication.id }}" class="form-label">Select Supplier</label>
                        <select name="supplier" id="supplier{{ medication.id }}" class="form-select supplier-select" required>
                            <option value="">Choose supplier...</option>
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="quantity{{ medication.id }}" class="form-label">Quantity</label>
                            <input type="number" name="quantity" id="quantity{{ medication.id }}"
                                   class="form-control" min="1" required>
                        </div>
                        <div class="col-md-6">
                            <label for="unit_price{{ medication.id }}" class="form-label">Unit Price</label>
                            <input type="number" name="unit_price" id="unit_price{{ medication.id }}"
                                   class="form-control" step="0.01" min="0" value="{{ medication.price }}" required>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label for="notes{{ medication.id }}" class="form-label">Notes (Optional)</label>
                        <textarea name="notes" id="notes{{ medication.id }}" class="form-control" rows="2" placeholder="Add notes about this procurement request..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-shopping-cart"></i> Create Procurement Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for category dropdown
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });

        // Load suppliers for procurement modals
        fetch('{% url "pharmacy:api_suppliers" %}')
            .then(response => response.json())
            .then(suppliers => {
                document.querySelectorAll('.supplier-select').forEach(select => {
                    suppliers.forEach(supplier => {
                        const option = document.createElement('option');
                        option.value = supplier.id;
                        option.textContent = supplier.name;
                        select.appendChild(option);
                    });
                });
            })
            .catch(error => console.error('Error loading suppliers:', error));
    });
</script>
{% endblock %}
