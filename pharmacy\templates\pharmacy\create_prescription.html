{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                </div>
                <div class="card-body">
                    {% if patient %}
                    <!-- Patient Information Section -->
                    <div class="alert alert-info">
                        <h5 class="alert-heading">
                            <i class="fas fa-user"></i> Creating Prescription for Selected Patient
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Patient:</strong> {{ patient.get_full_name }}<br>
                                <strong>Patient ID:</strong> {{ patient.patient_id }}<br>
                                <strong>Phone:</strong> {{ patient.phone_number|default:"N/A" }}
                            </div>
                            <div class="col-md-6">
                                <strong>Age:</strong> {{ patient.get_age }} years<br>
                                <strong>Gender:</strong> {{ patient.get_gender_display }}<br>
                                {% if patient.nhia_info %}
                                <strong>NHIA:</strong> <span class="badge bg-success">{{ patient.nhia_info.nhia_reg_number }}</span>
                                {% else %}
                                <strong>NHIA:</strong> <span class="badge bg-secondary">Not Registered</span>
                                {% endif %}
                            </div>
                        </div>
                        <hr class="my-2">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Patient has been automatically selected. You can focus on adding medications and prescription details.
                        </small>
                    </div>
                    {% endif %}

                    <form method="post" id="prescription-form">
                        {% csrf_token %}

                        <h4>Prescription Details</h4>
                        {% if patient %}
                            <!-- Hidden field for preselected patient -->
                            {{ prescription_form.patient_hidden }}
                            <!-- Show patient field as read-only -->
                            <div class="mb-3">
                                <label class="form-label">Patient</label>
                                <input type="text" class="form-control" value="{{ patient.get_full_name }} ({{ patient.patient_id }})" readonly style="background-color: #e9ecef;">
                                <small class="form-text text-muted">Patient is preselected from patient detail page</small>
                            </div>
                        {% else %}
                            {{ prescription_form.patient|as_crispy_field }}
                        {% endif %}
                        {{ prescription_form.doctor|as_crispy_field }}
                        {{ prescription_form.prescription_date|as_crispy_field }}
                        {{ prescription_form.diagnosis|as_crispy_field }}
                        {{ prescription_form.prescription_type|as_crispy_field }}
                        {{ prescription_form.notes|as_crispy_field }}

                        <hr>
                        <h4>Medications</h4>
                        <div id="medication-items-container">
                            {{ medication_formset.management_form }}
                            {% for form in medication_formset %}
                                <div class="form-row medication-item mb-3 p-3 border rounded bg-light">
                                    {% for field in form %}
                                        <div class="col-md-4">
                                            {{ field.label_tag }}
                                            {{ field }}
                                            {% if field.help_text %}
                                                <small class="form-text text-muted">{{ field.help_text }}</small>
                                            {% endif %}
                                            {% for error in field.errors %}
                                                <div class="invalid-feedback d-block">{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endfor %}
                                    <div class="col-md-12 text-right mt-2">
                                        <button type="button" class="btn btn-danger btn-sm remove-medication-item"><i class="fas fa-trash"></i> Remove</button>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        <button type="button" class="btn btn-success btn-sm" id="add-medication-item"><i class="fas fa-plus"></i> Add Another Medication</button>

                        <div id="empty-form-template" style="display: none;">
                            <div class="form-row medication-item mb-3 p-3 border rounded bg-light">
                                {% for field in medication_formset.empty_form %}
                                    <div class="col-md-4">
                                        {{ field.label_tag }}
                                        {{ field }}
                                        {% if field.help_text %}
                                            <small class="form-text text-muted">{{ field.help_text }}</small>
                                        {% endif %}
                                        {% for error in field.errors %}
                                            <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endfor %}
                                <div class="col-md-12 text-right mt-2">
                                    <button type="button" class="btn btn-danger btn-sm remove-medication-item"><i class="fas fa-trash"></i> Remove</button>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">Create Prescription</button>
                            <a href="{% url 'pharmacy:prescriptions' %}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('.select2').select2();

        // Function to initialize Select2 on a new element
        function initializeSelect2(element) {
            $(element).select2();
        }

        // Add Medication Item
        $('#add-medication-item').click(function() {
            var newItem = $('.medication-item:first').clone();
            newItem.find('input, textarea').val(''); // Clear values
            newItem.find('select').val('').trigger('change'); // Clear selected option and trigger change for Select2
            newItem.find('.select2-container').remove(); // Remove old Select2 instance
            $('#medication-items-container').append(newItem);
            initializeSelect2(newItem.find('select')); // Initialize Select2 on the new select
        });

        // Remove Medication Item
        $(document).on('click', '.remove-medication-item', function() {
            if ($('.medication-item').length > 1) {
                $(this).closest('.medication-item').remove();
            } else {
                alert("You must have at least one medication item.");
            }
        });
    });
</script>
{% endblock %}