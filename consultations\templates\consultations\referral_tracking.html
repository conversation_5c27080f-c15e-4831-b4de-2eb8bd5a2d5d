{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            <a href="{% url 'consultations:create_referral' %}" class="btn btn-success">
                <i class="fas fa-plus"></i> New Referral
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Referrals</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_referrals }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Referrals</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_referrals }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Completed Referrals</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ completed_referrals }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter"></i> Search & Filter Referrals
            </h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="patient" class="form-label">Patient Search</label>
                    <input type="text" name="patient" id="patient" class="form-control" 
                           placeholder="Patient name or ID..." value="{{ patient_search }}">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                        <option value="accepted" {% if status_filter == 'accepted' %}selected{% endif %}>Accepted</option>
                        <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Completed</option>
                        <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="doctor" class="form-label">Doctor</label>
                    <select name="doctor" id="doctor" class="form-select">
                        <option value="">All Doctors</option>
                        {% for doc in doctors %}
                        <option value="{{ doc.id }}" {% if doctor_filter == doc.id|stringformat:"s" %}selected{% endif %}>
                            {{ doc.get_full_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" name="date_from" id="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" name="date_to" id="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Referrals List -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list"></i> Referrals ({{ referrals.paginator.count }} total)
            </h6>
        </div>
        <div class="card-body">
            {% if referrals %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Patient</th>
                            <th>From</th>
                            <th>To</th>
                            <th>Reason</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for referral in referrals %}
                        <tr>
                            <td>{{ referral.referral_date|date:"M d, Y" }}</td>
                            <td>
                                <a href="{% url 'patients:patient_detail' referral.patient.id %}">
                                    {{ referral.patient.get_full_name }}
                                </a>
                                <small class="text-muted d-block">{{ referral.patient.patient_id }}</small>
                            </td>
                            <td>{{ referral.referring_doctor.get_full_name }}</td>
                            <td>{{ referral.referred_to.get_full_name }}</td>
                            <td>{{ referral.reason|truncatechars:50 }}</td>
                            <td>
                                <span class="badge {% if referral.status == 'pending' %}bg-warning{% elif referral.status == 'accepted' %}bg-info{% elif referral.status == 'completed' %}bg-success{% else %}bg-danger{% endif %}">
                                    {{ referral.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{% url 'consultations:referral_detail' referral.id %}" 
                                       class="btn btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if referral.referred_to == request.user or referral.referring_doctor == request.user %}
                                    <button type="button" class="btn btn-outline-info" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#updateStatusModal{{ referral.id }}" 
                                            title="Update Status">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% endif %}
                                    {% if referral.referred_to == request.user and referral.status == 'pending' %}
                                    <a href="{% url 'consultations:create_consultation' %}?patient={{ referral.patient.id }}" 
                                       class="btn btn-outline-success" title="Start Consultation">
                                        <i class="fas fa-stethoscope"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if referrals.has_other_pages %}
            <nav aria-label="Referrals pagination">
                <ul class="pagination justify-content-center">
                    {% if referrals.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if status_filter %}&status={{ status_filter }}{% endif %}{% if patient_search %}&patient={{ patient_search }}{% endif %}{% if doctor_filter %}&doctor={{ doctor_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ referrals.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if patient_search %}&patient={{ patient_search }}{% endif %}{% if doctor_filter %}&doctor={{ doctor_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}

                    {% for num in referrals.paginator.page_range %}
                        {% if referrals.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > referrals.number|add:'-3' and num < referrals.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if patient_search %}&patient={{ patient_search }}{% endif %}{% if doctor_filter %}&doctor={{ doctor_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if referrals.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ referrals.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if patient_search %}&patient={{ patient_search }}{% endif %}{% if doctor_filter %}&doctor={{ doctor_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ referrals.paginator.num_pages }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if patient_search %}&patient={{ patient_search }}{% endif %}{% if doctor_filter %}&doctor={{ doctor_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">Last</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No referrals found</h5>
                <p class="text-muted">
                    {% if status_filter or patient_search or doctor_filter or date_from or date_to %}
                        No referrals match your search criteria. Try adjusting your filters.
                    {% else %}
                        No referrals have been created yet.
                    {% endif %}
                </p>
                <a href="{% url 'consultations:create_referral' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create First Referral
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Update Status Modals -->
{% for referral in referrals %}
{% if referral.referred_to == request.user or referral.referring_doctor == request.user %}
<div class="modal fade" id="updateStatusModal{{ referral.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Status - {{ referral.patient.get_full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'consultations:update_referral_status_detailed' referral.id %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="status{{ referral.id }}" class="form-label">Status</label>
                        <select name="status" id="status{{ referral.id }}" class="form-select" required>
                            {% for value, label in referral.STATUS_CHOICES %}
                            <option value="{{ value }}" {% if value == referral.status %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="status_notes{{ referral.id }}" class="form-label">Notes (Optional)</label>
                        <textarea name="status_notes" id="status_notes{{ referral.id }}" class="form-control" rows="3" placeholder="Add notes about this status change..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}
{% endblock %}
