{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  Patient: {{ patient.get_full_name }} | {{ block.super }}
{% endblock title %}

{% block page_title %}
  Patient Details: {{ patient.get_full_name }} ({{ patient.patient_id }})
{% endblock page_title %}

{% block breadcrumbs %}
  <li class="breadcrumb-item"><a href="{% url 'patients:list' %}">Patients</a></li>
  <li class="breadcrumb-item active" aria-current="page">{{ patient.get_full_name }}</li>
{% endblock breadcrumbs %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Patient Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ patient.get_full_name }}</p>
                <p><strong>Patient ID:</strong> {{ patient.patient_id }}</p>
                <p><strong>Type:</strong> {{ patient.get_patient_type_display }}</p>
                <p><strong>Date of Birth:</strong> {{ patient.date_of_birth|date:"Y-m-d" }} (Age: {{ age }})</p>
                <p><strong>Gender:</strong> {{ patient.get_gender_display }}</p>
                <p><strong>Phone:</strong> {{ patient.phone_number }}</p>
                <p><strong>Email:</strong> {{ patient.email|default:"N/A" }}</p>
                <p><strong>Address:</strong> {{ patient.address }}, {{ patient.city }}, {{ patient.state }} {{ patient.postal_code }}, {{ patient.country }}</p>
                <p><strong>Registration Date:</strong> {{ patient.registration_date|date:"Y-m-d H:i" }}</p>
                <p><strong>Status:</strong> {% if patient.is_active %}<span class="badge bg-success">Active</span>{% else %}<span class="badge bg-danger">Inactive</span> <span class="text-danger ms-2">This patient is currently inactive. To reactivate, use the button below.</span>{% endif %}</p>

                <!-- TEMPORARY TEST MESSAGE FOR DEBUGGING -->
                <div style="background: #ff0066; color: #fff; padding: 1rem; font-size: 1.5rem; text-align: center; margin-bottom: 1.5rem;">
                  TEST: This message confirms the patient_detail.html template is rendering. If you see this, the template block is active.
                </div>
                <!-- END TEMPORARY TEST MESSAGE -->

                <!-- TEST: ACTIVATION BUTTON BLOCK RENDERING -->
                <div style="background: #ffeb3b; color: #d32f2f; padding: 8px; margin-bottom: 8px; font-weight: bold; border: 2px solid #d32f2f;">
                  TEST: You are seeing this message because the activation/deactivation button block is being rendered.
                </div>
                {% if not patient.is_active %}
                    <form method="post" action="{% url 'patients:toggle_patient_status' patient.id %}" style="margin-bottom: 1rem;">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-success">Activate Patient</button>
                    </form>
                {% elif patient.is_active %}
                    <form method="post" action="{% url 'patients:toggle_patient_status' patient.id %}" style="margin-bottom: 1rem;">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-warning">Deactivate Patient</button>
                    </form>
                {% endif %}
                

                <!-- Wallet Information -->
                <h5>Wallet Information</h5>
                {% if has_wallet and wallet_is_active %}
                    <p><strong>Wallet Balance:</strong> ₦{{ patient.wallet.balance|floatformat:2 }}</p>
                    <p><strong>Wallet Status:</strong>
                        {% if patient.wallet.is_active %}
                            <span class="badge bg-success">Active</span>
                        {% else %}
                            <span class="badge bg-danger">Inactive</span>
                        {% endif %}
                    </p>
                    <div class="btn-group" role="group">
                        <a href="{% url 'patients:wallet_dashboard' patient.id %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-dashboard"></i> Wallet Dashboard
                        </a>
                        <a href="{% url 'patients:add_funds_to_wallet' patient.id %}" class="btn btn-success btn-sm">
                            <i class="fas fa-plus"></i> Add Funds
                        </a>
                        <a href="{% url 'patients:wallet_transactions' patient.id %}" class="btn btn-info btn-sm">
                            <i class="fas fa-list"></i> Transactions
                        </a>
                    </div>
                {% elif has_wallet and not wallet_is_active %}
                    <p>Wallet is currently inactive.</p>
                    <p><strong>Wallet Balance:</strong> ₦{{ patient.wallet.balance|floatformat:2 }}</p>
                     <a href="{% url 'patients:wallet_dashboard' patient.id %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-dashboard"></i> Wallet Dashboard
                    </a>
                {% else %}
                    <p>No wallet found for this patient.</p>
                    <a href="{% url 'patients:add_funds_to_wallet' patient.id %}" class="btn btn-info btn-sm">
                        <i class="fas fa-wallet"></i> Create Wallet & Add Funds
                    </a>
                {% endif %}
                <hr>

                <!-- NHIA Information -->
                <h5>NHIA Information</h5>
                {% if nhia_info %}
                    <p><strong>NHIA Registration Number:</strong> {{ nhia_info.nhia_reg_number }}</p>
                    <p><strong>NHIA Status:</strong> {% if nhia_info.is_active %}<span class="badge bg-success">Active</span>{% else %}<span class="badge bg-danger">Inactive</span>{% endif %}</p>
                    <a href="{% url 'patients:edit_nhia_patient' patient.id %}" class="btn btn-info btn-sm">
                        <i class="fas fa-edit"></i> Edit NHIA Record
                    </a>
                {% else %}
                    <p>No NHIA record found for this patient.</p>
                    <a href="{% url 'patients:register_nhia_patient' patient.id %}" class="btn btn-success btn-sm">
                        <i class="fas fa-plus"></i> Register NHIA Record
                    </a>
                {% endif %}
                <hr>

                <!-- Recent Admissions -->
                <h5>Recent Admissions</h5>
                {% if recent_admissions %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Date</th>
                                    <th>Billed</th>
                                    <th>Paid</th>
                                    <th>Balance</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for admission in recent_admissions %}
                                <tr>
                                    <td>{{ admission.id }}</td>
                                    <td>{{ admission.admission_date|date:"Y-m-d" }}</td>
                                    <td>₦{{ admission.billed_amount|floatformat:2 }}</td>
                                    <td>₦{{ admission.amount_paid|floatformat:2 }}</td>
                                    <td>₦{{ admission.billed_amount|sub:admission.amount_paid|floatformat:2 }}</td>
                                    <td>
                                        {% if admission.get_total_cost <= admission.amount_paid %}
                                            <span class="badge bg-success">Paid</span>
                                        {% elif admission.amount_paid > 0 %}
                                            <span class="badge bg-warning">Partially Paid</span>
                                        {% else %}
                                            <span class="badge bg-danger">Pending</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'inpatient:admission_detail' admission.id %}" class="btn btn-info btn-sm">View</a>
                                        {% if admission.get_total_cost > admission.amount_paid %}
                                            <a href="{% url 'billing:create_invoice_for_admission' admission.id %}" class="btn btn-success btn-sm">Pay</a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p>No recent admissions found for this patient.</p>
                {% endif %}
                <hr>

                <a href="{% url 'patients:edit' patient.id %}" class="btn btn-primary">Edit Patient</a>
            </div>
        </div>

        <!-- Medical History Section (Placeholder) -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title">Medical History</h5>
            </div>
            <div class="card-body">
                <p><em>Medical history details will be displayed here.</em></p>
                <!-- Add Medical History Form/Button -->
            </div>
        </div>

        <!-- Vitals Section (Placeholder) -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title">Vitals</h5>
            </div>
            <div class="card-body">
                <p><em>Patient vitals will be displayed here.</em></p>
                <!-- Add Vitals Form/Button -->
            </div>
        </div>

    </div>
    <div class="col-md-4">
        <!-- Patient Profile Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Patient Profile</h5>
            </div>
            <div class="card-body text-center">
                {% if patient.has_profile_image %}
                    <img src="{{ patient.get_profile_image_url }}" alt="{{ patient.get_full_name }}" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover; border: 3px solid #dee2e6;">
                {% else %}
                    <img src="{% static 'img/undraw_profile.svg' %}" alt="Default Profile" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover; border: 3px solid #dee2e6;">
                {% endif %}
                <h5>{{ patient.get_full_name }}</h5>
                <p class="text-muted">Patient type: {{ patient.get_patient_type_display }}</p>
                <p class="text-muted">ID: {{ patient.patient_id }}</p>
                {% if patient.is_active %}
                    <span class="badge bg-success">Active Patient</span>
                {% else %}
                    <span class="badge bg-danger">Inactive Patient</span>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions Card -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Quick Actions</h5>
            </div>
            <div class="card-body">
                <a href="#" class="btn btn-info btn-block mb-2">New Appointment</a>
                <a href="{% url 'consultations:create_consultation' patient.id %}" class="btn btn-warning btn-block mb-2">New Consultation</a>
                <a href="{% url 'consultations:create_referral' %}" class="btn btn-info btn-block mb-2">Refer Patient</a>
                <a href="{% url 'pharmacy:create_prescription' patient.id %}" class="btn btn-success btn-block mb-2" style="display: block !important; visibility: visible !important;">Send Medication Prescription</a>
                <a href="{% url 'pharmacy:pharmacy_create_prescription' patient.id %}" class="btn btn-primary btn-block mb-2">Pharmacy Prescription</a>
                <a href="{% url 'laboratory:create_test_request' %}?patient={{ patient.id }}" class="btn btn-secondary btn-block mb-2">Request Lab Tests</a>
                <a href="{% url 'patients:edit' patient.id %}" class="btn btn-primary btn-block mb-2">Edit Patient</a>
                <a href="{% url 'patients:wallet_dashboard' patient.id %}" class="btn btn-success btn-block mb-2">Wallet Dashboard</a>
            </div>
        </div>
    </div>
</div>


{% endblock content %}