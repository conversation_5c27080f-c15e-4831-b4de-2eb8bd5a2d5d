{% extends "base.html" %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .inventory-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .stats-card {
        transition: transform 0.2s ease-in-out;
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }

    .search-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    }

    .inventory-table {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    }

    .table th {
        background: #f8f9fc;
        border: none;
        font-weight: 600;
        color: #5a5c69;
        padding: 15px;
    }

    .table td {
        padding: 15px;
        vertical-align: middle;
        border-color: #e3e6f0;
    }

    .medication-name {
        font-weight: 600;
        color: #2c3e50;
    }

    .stock-badge {
        font-size: 0.8rem;
        padding: 5px 10px;
        border-radius: 20px;
    }

    .action-buttons .btn {
        margin: 2px;
        border-radius: 6px;
    }

    .empty-state {
        padding: 60px 20px;
        text-align: center;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Enhanced Header -->
    <div class="inventory-header">
        <div class="d-flex flex-column flex-md-row align-items-start align-items-md-center justify-content-between">
            <div class="mb-3 mb-md-0">
                <h1 class="h2 mb-2">
                    <i class="fas fa-pills"></i> {{ title }}
                </h1>
                <p class="mb-0 opacity-75">Manage your medication inventory, track stock levels, and handle procurement</p>
            </div>
            <div class="d-flex flex-wrap gap-2">
                <a href="{% url 'pharmacy:add_medication' %}" class="btn btn-light btn-sm">
                    <i class="fas fa-plus"></i> Add Medication
                </a>
                <a href="{% url 'pharmacy:supplier_list' %}" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-truck"></i> Suppliers
                </a>
            </div>
        </div>
    </div>

    <!-- Enhanced Search and Filter Section -->
    <div class="search-card card mb-4">
        <div class="card-header bg-white py-3 border-0">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-search"></i> Search & Filter Medications
            </h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-lg-4 col-md-6">
                    <label for="{{ form.search.id_for_label }}" class="form-label fw-semibold">
                        <i class="fas fa-search text-muted me-1"></i> Search Medications
                    </label>
                    {{ form.search }}
                    {% if form.search.errors %}
                        <div class="text-danger small">{{ form.search.errors }}</div>
                    {% endif %}
                </div>
                <div class="col-lg-3 col-md-6">
                    <label for="{{ form.category.id_for_label }}" class="form-label fw-semibold">
                        <i class="fas fa-tags text-muted me-1"></i> Category
                    </label>
                    {{ form.category }}
                    {% if form.category.errors %}
                        <div class="text-danger small">{{ form.category.errors }}</div>
                    {% endif %}
                </div>
                <div class="col-lg-3 col-md-6">
                    <label for="{{ form.is_active.id_for_label }}" class="form-label fw-semibold">
                        <i class="fas fa-toggle-on text-muted me-1"></i> Status
                    </label>
                    {{ form.is_active }}
                    {% if form.is_active.errors %}
                        <div class="text-danger small">{{ form.is_active.errors }}</div>
                    {% endif %}
                </div>
                <div class="col-lg-2 col-md-6 d-flex align-items-end">
                    <div class="d-flex gap-2 w-100">
                        <button type="submit" class="btn btn-primary flex-fill">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <a href="{% url 'pharmacy:inventory' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Enhanced Quick Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card card border-0 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Medications
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">{{ page_obj.paginator.count }}</div>
                        </div>
                        <div class="ms-3">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-pills fa-lg text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card card border-0 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Medications
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">
                                {% for med in page_obj %}{% if med.is_active %}{{ forloop.counter }}{% endif %}{% endfor %}
                            </div>
                        </div>
                        <div class="ms-3">
                            <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-check-circle fa-lg text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card card border-0 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Low Stock Items
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">
                                {% comment %}This would need to be calculated in the view{% endcomment %}
                                0
                            </div>
                        </div>
                        <div class="ms-3">
                            <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-exclamation-triangle fa-lg text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card card border-0 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Quick Actions
                            </div>
                            <div class="mt-2">
                                <a href="{% url 'pharmacy:add_medication' %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus"></i> Add Medication
                                </a>
                            </div>
                        </div>
                        <div class="ms-3">
                            <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-bolt fa-lg text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Medication Inventory List -->
    <div class="inventory-table card border-0 mb-4">
        <div class="card-header bg-white py-4 border-0">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                <h6 class="m-0 font-weight-bold text-primary mb-2 mb-md-0">
                    <i class="fas fa-pills"></i> Medication Inventory
                    <span class="badge bg-primary ms-2">{{ page_obj.paginator.count }} total</span>
                </h6>
                <div class="d-flex flex-wrap gap-2">
                    <a href="{% url 'pharmacy:supplier_list' %}" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-truck"></i> Suppliers
                    </a>
                    <a href="{% url 'pharmacy:add_medication' %}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus"></i> Add Medication
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover mb-0" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th class="border-0">Medication Details</th>
                            <th class="border-0">Category</th>
                            <th class="border-0">Form & Strength</th>
                            <th class="border-0">Price</th>
                            <th class="border-0">Stock Status</th>
                            <th class="border-0">Status</th>
                            <th class="border-0 text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for medication in page_obj %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                        <i class="fas fa-pills text-primary"></i>
                                    </div>
                                    <div>
                                        <div class="medication-name">{{ medication.name }}</div>
                                        {% if medication.generic_name %}
                                        <small class="text-muted">{{ medication.generic_name }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if medication.category %}
                                <span class="badge bg-light text-dark border">{{ medication.category.name }}</span>
                                {% else %}
                                <span class="text-muted">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    <span class="fw-semibold text-primary">{{ medication.dosage_form }}</span>
                                    {% if medication.strength %}
                                    <small class="d-block text-muted">{{ medication.strength }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="fw-bold text-success">₦{{ medication.price|floatformat:2 }}</span>
                            </td>
                            <td>
                                {% for inventory in medication.inventories.all %}
                                    <div class="mb-1">
                                        <small class="text-muted fw-semibold">{{ inventory.dispensary.name }}:</small>
                                        <span class="stock-badge badge {% if inventory.is_low_stock %}bg-warning text-dark{% elif inventory.stock_quantity > 0 %}bg-success{% else %}bg-danger{% endif %}">
                                            {{ inventory.stock_quantity }} units
                                        </span>
                                        {% if inventory.is_low_stock %}
                                        <small class="text-warning d-block">
                                            <i class="fas fa-exclamation-triangle"></i> Low Stock!
                                        </small>
                                        {% endif %}
                                    </div>
                                {% empty %}
                                    <span class="stock-badge badge bg-secondary">No inventory</span>
                                {% endfor %}
                            </td>
                            <td>
                                {% if medication.is_active %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> Active
                                    </span>
                                {% else %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times"></i> Inactive
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="action-buttons d-flex justify-content-center">
                                    <a href="{% url 'pharmacy:medication_detail' medication.id %}"
                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'pharmacy:edit_medication' medication.id %}"
                                       class="btn btn-sm btn-outline-info" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-success"
                                            data-bs-toggle="modal"
                                            data-bs-target="#procureModal{{ medication.id }}"
                                            title="Procure from Supplier">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                    {% if medication.is_active %}
                                    <a href="{% url 'pharmacy:delete_medication' medication.id %}"
                                       class="btn btn-sm btn-outline-danger" title="Deactivate">
                                        <i class="fas fa-ban"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Enhanced Pagination -->
            {% if page_obj.paginator.num_pages > 1 %}
            <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                <div class="text-muted">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} medications
                </div>
                <nav aria-label="Medication pagination">
                    <ul class="pagination mb-0">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            </li>
                        {% endif %}

                        {% for i in page_obj.paginator.page_range %}
                            {% if page_obj.number == i %}
                                <li class="page-item active">
                                    <span class="page-link">{{ i }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ i }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}">{{ i }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}

            {% else %}
            <div class="empty-state">
                <i class="fas fa-pills"></i>
                <h4 class="mb-3">No medications found</h4>
                <p class="mb-4">
                    {% if request.GET.search or request.GET.category or request.GET.is_active %}
                        No medications match your search criteria. Try adjusting your filters or clearing the search.
                    {% else %}
                        Your medication inventory is empty. Get started by adding your first medication.
                    {% endif %}
                </p>
                <div class="d-flex gap-2 justify-content-center">
                    <a href="{% url 'pharmacy:add_medication' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Medication
                    </a>
                    {% if request.GET.search or request.GET.category or request.GET.is_active %}
                    <a href="{% url 'pharmacy:inventory' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear Filters
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Procurement Modals -->
{% for medication in page_obj %}
<div class="modal fade" id="procureModal{{ medication.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Procure {{ medication.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'pharmacy:create_procurement_request' medication.id %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="supplier{{ medication.id }}" class="form-label">Select Supplier</label>
                        <select name="supplier" id="supplier{{ medication.id }}" class="form-select" required>
                            <option value="">Choose supplier...</option>
                            <!-- This will be populated by JavaScript or server-side -->
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="quantity{{ medication.id }}" class="form-label">Quantity</label>
                            <input type="number" name="quantity" id="quantity{{ medication.id }}"
                                   class="form-control" min="1" required>
                        </div>
                        <div class="col-md-6">
                            <label for="unit_price{{ medication.id }}" class="form-label">Unit Price</label>
                            <input type="number" name="unit_price" id="unit_price{{ medication.id }}"
                                   class="form-control" step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label for="notes{{ medication.id }}" class="form-label">Notes (Optional)</label>
                        <textarea name="notes" id="notes{{ medication.id }}" class="form-control" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-shopping-cart"></i> Create Procurement Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endfor %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load suppliers for procurement modals
    fetch('{% url "pharmacy:api_suppliers" %}')
        .then(response => response.json())
        .then(suppliers => {
            document.querySelectorAll('[id^="supplier"]').forEach(select => {
                suppliers.forEach(supplier => {
                    const option = document.createElement('option');
                    option.value = supplier.id;
                    option.textContent = supplier.name;
                    select.appendChild(option);
                });
            });
        })
        .catch(error => console.error('Error loading suppliers:', error));
});
</script>
{% endblock %}