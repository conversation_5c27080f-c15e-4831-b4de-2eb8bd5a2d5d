{% extends "base.html" %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{% url 'pharmacy:add_medication' %}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> Add New Medication
        </a>
    </div>

    <!-- Search and Filter Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-search"></i> Search & Filter Medications
            </h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="{{ form.search.id_for_label }}" class="form-label">Search</label>
                    {{ form.search }}
                    {% if form.search.errors %}
                        <div class="text-danger">{{ form.search.errors }}</div>
                    {% endif %}
                </div>
                <div class="col-md-3">
                    <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                    {{ form.category }}
                    {% if form.category.errors %}
                        <div class="text-danger">{{ form.category.errors }}</div>
                    {% endif %}
                </div>
                <div class="col-md-3">
                    <label for="{{ form.is_active.id_for_label }}" class="form-label">Status</label>
                    {{ form.is_active }}
                    {% if form.is_active.errors %}
                        <div class="text-danger">{{ form.is_active.errors }}</div>
                    {% endif %}
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Search
                    </button>
                    <a href="{% url 'pharmacy:inventory' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Medications</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ page_obj.paginator.count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-pills fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Medications</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% for med in page_obj %}{% if med.is_active %}{{ forloop.counter }}{% endif %}{% endfor %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Categories</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ page_obj|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Quick Actions</div>
                            <div class="mt-2">
                                <a href="{% url 'pharmacy:add_medication' %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus"></i> Add Medication
                                </a>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bolt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Medication Inventory List -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-pills"></i> Medication Inventory ({{ page_obj.paginator.count }} total)
            </h6>
            <div>
                <a href="{% url 'pharmacy:supplier_list' %}" class="btn btn-sm btn-info">
                    <i class="fas fa-truck"></i> Manage Suppliers
                </a>
                <a href="{% url 'pharmacy:add_medication' %}" class="btn btn-sm btn-success">
                    <i class="fas fa-plus"></i> Add Medication
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                    <thead class="table-light">
                        <tr>
                            <th>Medication</th>
                            <th>Category</th>
                            <th>Form & Strength</th>
                            <th>Price</th>
                            <th>Stock Status</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for medication in page_obj %}
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ medication.name }}</strong>
                                    {% if medication.generic_name %}
                                    <small class="text-muted d-block">{{ medication.generic_name }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if medication.category %}
                                <span class="badge bg-secondary">{{ medication.category.name }}</span>
                                {% else %}
                                <span class="text-muted">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    <span class="text-primary">{{ medication.dosage_form }}</span>
                                    {% if medication.strength %}
                                    <small class="d-block text-muted">{{ medication.strength }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <strong class="text-success">₦{{ medication.price }}</strong>
                            </td>
                            <td>
                                {% for inventory in medication.inventories.all %}
                                    <div class="mb-1">
                                        <small class="text-muted">{{ inventory.dispensary.name }}:</small>
                                        <span class="badge {% if inventory.is_low_stock %}bg-warning{% elif inventory.stock_quantity > 0 %}bg-success{% else %}bg-danger{% endif %}">
                                            {{ inventory.stock_quantity }} units
                                        </span>
                                        {% if inventory.is_low_stock %}
                                        <small class="text-warning d-block">Low Stock!</small>
                                        {% endif %}
                                    </div>
                                {% empty %}
                                    <span class="badge bg-secondary">No inventory</span>
                                {% endfor %}
                            </td>
                            <td>
                                {% if medication.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{% url 'pharmacy:medication_detail' medication.id %}"
                                       class="btn btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'pharmacy:edit_medication' medication.id %}"
                                       class="btn btn-outline-info" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-success"
                                            data-bs-toggle="modal"
                                            data-bs-target="#procureModal{{ medication.id }}"
                                            title="Procure from Supplier">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                    {% if medication.is_active %}
                                    <a href="{% url 'pharmacy:delete_medication' medication.id %}"
                                       class="btn btn-outline-danger" title="Deactivate">
                                        <i class="fas fa-ban"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <nav aria-label="Page navigation example">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}">Previous</a></li>
                    {% endif %}

                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                            <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                        {% else %}
                            <li class="page-item"><a class="page-link" href="?page={{ i }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}">{{ i }}</a></li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}">Next</a></li>
                    {% endif %}
                </ul>
            </nav>

            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No medications found</h5>
                <p class="text-muted">
                    {% if request.GET.search or request.GET.category or request.GET.is_active %}
                        No medications match your search criteria. Try adjusting your filters.
                    {% else %}
                        Get started by adding your first medication.
                    {% endif %}
                </p>
                <a href="{% url 'pharmacy:add_medication' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add First Medication
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Procurement Modals -->
{% for medication in page_obj %}
<div class="modal fade" id="procureModal{{ medication.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Procure {{ medication.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'pharmacy:create_procurement_request' medication.id %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="supplier{{ medication.id }}" class="form-label">Select Supplier</label>
                        <select name="supplier" id="supplier{{ medication.id }}" class="form-select" required>
                            <option value="">Choose supplier...</option>
                            <!-- This will be populated by JavaScript or server-side -->
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="quantity{{ medication.id }}" class="form-label">Quantity</label>
                            <input type="number" name="quantity" id="quantity{{ medication.id }}"
                                   class="form-control" min="1" required>
                        </div>
                        <div class="col-md-6">
                            <label for="unit_price{{ medication.id }}" class="form-label">Unit Price</label>
                            <input type="number" name="unit_price" id="unit_price{{ medication.id }}"
                                   class="form-control" step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label for="notes{{ medication.id }}" class="form-label">Notes (Optional)</label>
                        <textarea name="notes" id="notes{{ medication.id }}" class="form-control" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-shopping-cart"></i> Create Procurement Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endfor %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load suppliers for procurement modals
    fetch('{% url "pharmacy:api_suppliers" %}')
        .then(response => response.json())
        .then(suppliers => {
            document.querySelectorAll('[id^="supplier"]').forEach(select => {
                suppliers.forEach(supplier => {
                    const option = document.createElement('option');
                    option.value = supplier.id;
                    option.textContent = supplier.name;
                    select.appendChild(option);
                });
            });
        })
        .catch(error => console.error('Error loading suppliers:', error));
});
</script>
{% endblock %}